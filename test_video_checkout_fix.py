#!/usr/bin/env python3
"""
Test script to verify video product checkout fix
"""

import psycopg2
import psycopg2.extras
import json

def get_db_connection():
    """Get PostgreSQL database connection"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="sapmmo", 
            user="alandoan",
            password=""
        )
        return conn
    except Exception as e:
        print(f"Database connection error: {e}")
        return None

def test_video_checkout_validation():
    """Test video product checkout validation"""
    conn = get_db_connection()
    if not conn:
        print("❌ Failed to connect to database")
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("🧪 Testing Video Product Checkout Validation")
        print("=" * 50)
        
        # Find a video product with available stock
        cursor.execute('''
            SELECT 
                p.product_id,
                p.name,
                p.stock,
                p.price,
                COUNT(vl.link_id) as available_video_links
            FROM "Products" p
            LEFT JOIN "ProductVideoLinks" pvl ON p.product_id = pvl.product_id
            LEFT JOIN "VideoLinks" vl ON pvl.link_id = vl.link_id AND vl.status = 'available'
            WHERE p.product_type = 'videos' AND p.is_active = true
            GROUP BY p.product_id, p.name, p.stock, p.price
            ORDER BY available_video_links DESC, p.created_at DESC
            LIMIT 5
        ''')
        
        products = cursor.fetchall()
        
        print("📦 Available video products:")
        for product in products:
            status = "✅ Available" if product['available_video_links'] > 0 else "❌ Out of stock"
            print(f"   {status} {product['name']}")
            print(f"      Stock: {product['stock']} | Video links: {product['available_video_links']} | Price: {product['price']:,} MP")
        
        print()
        
        # Test case 1: Product with available stock
        available_product = None
        for product in products:
            if product['available_video_links'] > 0:
                available_product = product
                break
        
        if available_product:
            print(f"🧪 Test Case 1: Product with available stock")
            print(f"   Product: {available_product['name']}")
            print(f"   Available video links: {available_product['available_video_links']}")
            
            # Simulate checkout validation logic
            product_id = available_product['product_id']
            quantity = 1
            
            cursor.execute('''
                SELECT COUNT(*) FROM "VideoLinks" vl
                JOIN "ProductVideoLinks" pvl ON vl.link_id = pvl.link_id
                WHERE pvl.product_id = %s AND vl.status = 'available'
            ''', (product_id,))
            available_video_links = cursor.fetchone()[0]
            
            if available_video_links >= quantity:
                print(f"   ✅ PASS: Checkout validation should allow purchase")
                print(f"   Available: {available_video_links}, Requested: {quantity}")
            else:
                print(f"   ❌ FAIL: Checkout validation should block purchase")
                print(f"   Available: {available_video_links}, Requested: {quantity}")
        else:
            print("⚠️ No video products with available stock found for testing")
        
        print()
        
        # Test case 2: Product without available stock
        out_of_stock_product = None
        for product in products:
            if product['available_video_links'] == 0:
                out_of_stock_product = product
                break
        
        if out_of_stock_product:
            print(f"🧪 Test Case 2: Product without available stock")
            print(f"   Product: {out_of_stock_product['name']}")
            print(f"   Available video links: {out_of_stock_product['available_video_links']}")
            
            # Simulate checkout validation logic
            product_id = out_of_stock_product['product_id']
            quantity = 1
            
            cursor.execute('''
                SELECT COUNT(*) FROM "VideoLinks" vl
                JOIN "ProductVideoLinks" pvl ON vl.link_id = pvl.link_id
                WHERE pvl.product_id = %s AND vl.status = 'available'
            ''', (product_id,))
            available_video_links = cursor.fetchone()[0]
            
            if available_video_links >= quantity:
                print(f"   ❌ FAIL: Checkout validation should block purchase")
                print(f"   Available: {available_video_links}, Requested: {quantity}")
            else:
                print(f"   ✅ PASS: Checkout validation should block purchase")
                print(f"   Available: {available_video_links}, Requested: {quantity}")
                print(f"   Expected error: 'Sản phẩm \"{out_of_stock_product['name']}\" không đủ hàng. Còn lại {available_video_links} video links'")
        
        print()
        
        # Test case 3: Quantity exceeds available stock
        if available_product and available_product['available_video_links'] > 0:
            print(f"🧪 Test Case 3: Quantity exceeds available stock")
            print(f"   Product: {available_product['name']}")
            print(f"   Available video links: {available_product['available_video_links']}")
            
            product_id = available_product['product_id']
            quantity = available_product['available_video_links'] + 1  # Request more than available
            
            cursor.execute('''
                SELECT COUNT(*) FROM "VideoLinks" vl
                JOIN "ProductVideoLinks" pvl ON vl.link_id = pvl.link_id
                WHERE pvl.product_id = %s AND vl.status = 'available'
            ''', (product_id,))
            available_video_links = cursor.fetchone()[0]
            
            if available_video_links >= quantity:
                print(f"   ❌ FAIL: Checkout validation should block purchase")
                print(f"   Available: {available_video_links}, Requested: {quantity}")
            else:
                print(f"   ✅ PASS: Checkout validation should block purchase")
                print(f"   Available: {available_video_links}, Requested: {quantity}")
                print(f"   Expected error: 'Sản phẩm \"{available_product['name']}\" không đủ hàng. Còn lại {available_video_links} video links'")
        
        print()
        print("=" * 50)
        print("🎉 Video checkout validation test completed!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        cursor.close()
        conn.close()

def show_fix_summary():
    """Show summary of the fix applied"""
    print("🔧 Fix Summary:")
    print("=" * 50)
    print("Problem:")
    print("  - Checkout validation only checked stock in Products table")
    print("  - Did not verify actual available video links")
    print("  - Caused 'Không đủ hàng' error during checkout")
    print()
    print("Solution:")
    print("  1. Modified checkout API to check actual video links for video products")
    print("  2. Synchronized stock in Products table with available video links")
    print("  3. Added special validation logic for product_type = 'videos'")
    print()
    print("Code changes:")
    print("  - File: mip_system.py")
    print("  - Function: api_marketplace_checkout()")
    print("  - Added video-specific stock validation")
    print()
    print("Database sync:")
    print("  - Created sync_video_product_stock.py")
    print("  - Synchronized all video product stocks")
    print("  - Fixed stock mismatches")

if __name__ == "__main__":
    show_fix_summary()
    print("\n" + "="*50 + "\n")
    test_video_checkout_validation()
