<!DOCTYPE html>
<html>
<head>
    <title>Test Modal Select2</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
    
    <style>
        /* Fix Select2 z-index CHỈ trong modal */
        .modal .select2-container {
            z-index: 1050 !important;
        }
        
        .modal .select2-dropdown {
            z-index: 1060 !important;
        }
        
        /* Đảm bảo Select2 dropdown hiển thị đúng trong modal */
        .modal .select2-container--open .select2-dropdown {
            z-index: 1060 !important;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h2>Test Modal với Select2</h2>
        
        <!-- Button trigger modal -->
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#testModal">
            Mở Modal Test
        </button>

        <!-- Select2 bên ngoài modal -->
        <div class="mt-3">
            <label>Select2 bên ngoài modal:</label>
            <select class="form-select" id="outside_select">
                <option value="">Chọn option</option>
                <option value="1">Option 1</option>
                <option value="2">Option 2</option>
                <option value="3">Option 3</option>
            </select>
        </div>
    </div>

    <!-- Modal -->
    <div class="modal fade" id="testModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Test Select2 trong Modal</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label>Trạng thái:</label>
                        <select class="form-select" id="modal_status">
                            <option value="">Chọn trạng thái</option>
                            <option value="Live">Live</option>
                            <option value="Die">Die</option>
                            <option value="Not Available">Not Available</option>
                            <option value="Có giỏ">Có giỏ</option>
                            <option value="Đang nuôi">Đang nuôi</option>
                            <option value="Đủ điều kiện">Đủ điều kiện</option>
                            <option value="Bật hụt">Bật hụt</option>
                            <option value="Thu giỏ">Thu giỏ</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label>Đội:</label>
                        <select class="form-select" id="modal_team">
                            <option value="">Chọn đội</option>
                            <option value="1">Team 1</option>
                            <option value="2">Team 2</option>
                            <option value="3">Team 3</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                    <button type="button" class="btn btn-primary">Lưu</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Select2 bên ngoài modal (không có theme để tránh conflict)
            $('#outside_select').select2({
                placeholder: "Chọn option",
                allowClear: true,
                width: '100%'
            });

            // KEY: Khởi tạo select2 trong modal KHÔNG dùng dropdownParent
            $('#testModal').on('shown.bs.modal', function () {
                console.log('Modal shown - initializing Select2 WITHOUT dropdownParent');

                // Destroy existing select2 if any
                $('#testModal select').each(function() {
                    if ($(this).hasClass('select2-hidden-accessible')) {
                        $(this).select2('destroy');
                    }
                });

                // Initialize select2 - KEY: KHÔNG dùng dropdownParent
                $('#modal_status').select2({
                    theme: 'bootstrap-5',
                    placeholder: "Chọn trạng thái",
                    allowClear: true,
                    width: '100%'
                    // KHÔNG có dropdownParent: $('#testModal')
                });

                $('#modal_team').select2({
                    theme: 'bootstrap-5',
                    placeholder: "Chọn đội",
                    allowClear: true,
                    width: '100%'
                    // KHÔNG có dropdownParent: $('#testModal')
                });

                console.log('Select2 initialized in modal WITHOUT dropdownParent');
            });
        });
    </script>
</body>
</html>
