#!/usr/bin/env python3
"""
Script to synchronize video product stock with actual available video links
"""

import psycopg2
import psycopg2.extras

def get_db_connection():
    """Get PostgreSQL database connection"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="sapmmo", 
            user="alandoan",
            password=""
        )
        return conn
    except Exception as e:
        print(f"Database connection error: {e}")
        return None

def sync_video_product_stock():
    """Synchronize video product stock with available video links"""
    conn = get_db_connection()
    if not conn:
        print("❌ Failed to connect to database")
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("🔄 Synchronizing video product stock with available video links...")
        print("=" * 60)
        
        # Get all video products
        cursor.execute('''
            SELECT product_id, name, stock, unlimited_stock
            FROM "Products"
            WHERE product_type = 'videos' AND is_active = true
            ORDER BY created_at DESC
        ''')
        
        video_products = cursor.fetchall()
        
        if not video_products:
            print("ℹ️ No video products found")
            return True
        
        updated_count = 0
        
        for product in video_products:
            product_id = product['product_id']
            product_name = product['name']
            current_stock = product['stock']
            unlimited_stock = product['unlimited_stock']
            
            # Skip unlimited stock products
            if unlimited_stock:
                print(f"⏭️ Skipping {product_name} (unlimited stock)")
                continue
            
            # Count available video links for this product
            cursor.execute('''
                SELECT COUNT(*) as available_count
                FROM "VideoLinks" vl
                JOIN "ProductVideoLinks" pvl ON vl.link_id = pvl.link_id
                WHERE pvl.product_id = %s AND vl.status = 'available'
            ''', (product_id,))
            
            available_links = cursor.fetchone()['available_count']
            
            print(f"📦 {product_name}")
            print(f"   Current stock: {current_stock}")
            print(f"   Available video links: {available_links}")
            
            if current_stock != available_links:
                # Update stock to match available video links
                cursor.execute('''
                    UPDATE "Products"
                    SET stock = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE product_id = %s
                ''', (available_links, product_id))
                
                print(f"   ✅ Updated stock: {current_stock} → {available_links}")
                updated_count += 1
            else:
                print(f"   ✓ Stock already synchronized")
            
            print()
        
        conn.commit()
        
        print("=" * 60)
        print(f"🎉 Synchronization completed!")
        print(f"📊 Products updated: {updated_count}")
        print(f"📊 Total video products: {len(video_products)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during synchronization: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()
        return False
    finally:
        cursor.close()
        conn.close()

def check_video_product_status():
    """Check current status of video products"""
    conn = get_db_connection()
    if not conn:
        print("❌ Failed to connect to database")
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("🔍 Current video product status:")
        print("=" * 80)
        
        cursor.execute('''
            SELECT 
                p.product_id,
                p.name,
                p.stock,
                p.unlimited_stock,
                COUNT(vl.link_id) as available_video_links,
                COUNT(vl_sold.link_id) as sold_video_links
            FROM "Products" p
            LEFT JOIN "ProductVideoLinks" pvl ON p.product_id = pvl.product_id
            LEFT JOIN "VideoLinks" vl ON pvl.link_id = vl.link_id AND vl.status = 'available'
            LEFT JOIN "VideoLinks" vl_sold ON pvl.link_id = vl_sold.link_id AND vl_sold.status = 'sold'
            WHERE p.product_type = 'videos' AND p.is_active = true
            GROUP BY p.product_id, p.name, p.stock, p.unlimited_stock
            ORDER BY p.created_at DESC
        ''')
        
        products = cursor.fetchall()
        
        for product in products:
            product_name = product['name']
            stock = product['stock']
            unlimited_stock = product['unlimited_stock']
            available_links = product['available_video_links']
            sold_links = product['sold_video_links']
            
            status = "✅ OK" if stock == available_links or unlimited_stock else "❌ MISMATCH"
            
            print(f"{status} {product_name}")
            print(f"    Stock: {stock} | Available: {available_links} | Sold: {sold_links}")
            if unlimited_stock:
                print(f"    (Unlimited stock)")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking status: {e}")
        return False
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    print("🚀 Video Product Stock Synchronization Tool")
    print()
    
    # Check current status
    check_video_product_status()
    
    print("\n" + "="*80 + "\n")
    
    # Ask for confirmation
    response = input("Do you want to synchronize stock? (y/N): ").strip().lower()
    
    if response in ['y', 'yes']:
        sync_video_product_stock()
    else:
        print("ℹ️ Synchronization cancelled")
