#!/usr/bin/env python3
"""
Test script to verify "Video Nuôi Kênh" checkout fix
"""

import psycopg2
import psycopg2.extras

def get_db_connection():
    """Get PostgreSQL database connection"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="sapmmo", 
            user="alandoan",
            password=""
        )
        return conn
    except Exception as e:
        print(f"Database connection error: {e}")
        return None

def test_video_nuoi_kenh_checkout():
    """Test Video Nuôi Kênh checkout logic"""
    conn = get_db_connection()
    if not conn:
        print("❌ Failed to connect to database")
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("🧪 Testing 'Video Nuôi Kênh' Checkout Logic")
        print("=" * 50)
        
        # Get product details
        product_name = "Video Nuôi Kênh"
        cursor.execute('''
            SELECT product_id, name, price, stock, unlimited_stock, product_type, max_quantity,
                   (SELECT COUNT(*) FROM "OrderItems" oi
                    JOIN "Orders" o ON oi.order_id = o.order_id
                    WHERE oi.product_id = p.product_id AND o.status = 'completed') as sold_count
            FROM "Products" p
            WHERE name = %s AND is_active = true
        ''', (product_name,))
        
        product = cursor.fetchone()
        if not product:
            print(f"❌ Product '{product_name}' not found")
            return False
        
        product_id = product['product_id']
        unlimited_stock = product['unlimited_stock']
        original_stock = product['stock']
        product_type = product['product_type']
        sold_count = product['sold_count'] or 0
        
        print(f"📦 Product: {product['name']}")
        print(f"   ID: {product_id}")
        print(f"   Type: {product_type}")
        print(f"   Original stock: {original_stock}")
        print(f"   Unlimited stock: {unlimited_stock}")
        print(f"   Sold count: {sold_count}")
        
        # Calculate real-time stock (same logic as checkout)
        if unlimited_stock:
            real_stock = original_stock
        elif product_type == 'account':
            cursor.execute('''
                SELECT COUNT(*) FROM "PackageAccounts" pa
                JOIN "AccountPackages" ap ON pa.package_id = ap.package_id
                WHERE ap.product_id = %s AND (pa.is_sold = false OR pa.is_sold IS NULL)
            ''', (product_id,))
            real_stock = cursor.fetchone()[0] or 0
        elif product_type == 'videos':
            cursor.execute('''
                SELECT COUNT(*) FROM "ProductVideoLinks" WHERE product_id = %s
            ''', (product_id,))
            total_video_links = cursor.fetchone()[0] or 0
            real_stock = max(0, total_video_links - sold_count)
            print(f"   Total video links: {total_video_links}")
        else:
            real_stock = max(0, original_stock - sold_count)
        
        print(f"   Real-time stock: {real_stock}")
        print()
        
        # Test checkout validation for quantity = 1
        quantity = 1
        print(f"🔍 Testing checkout validation for quantity = {quantity}")
        
        if not unlimited_stock and real_stock < quantity:
            print(f"   ❌ BLOCKED: Sản phẩm '{product['name']}' không đủ hàng. Còn lại {real_stock}")
            checkout_allowed = False
        else:
            print(f"   ✅ ALLOWED: Checkout should proceed")
            checkout_allowed = True
        
        print()
        
        # Compare with API marketplace calculation
        print("🔄 Comparing with API marketplace calculation:")
        
        # Simulate API logic
        api_real_stock = max(0, total_video_links - sold_count) if product_type == 'videos' else real_stock
        print(f"   API real_stock: {api_real_stock}")
        print(f"   Checkout real_stock: {real_stock}")
        
        if api_real_stock == real_stock:
            print("   ✅ Stock calculations match!")
        else:
            print("   ❌ Stock calculations differ!")
        
        print()
        print("=" * 50)
        
        if checkout_allowed and real_stock > 0:
            print("🎉 SUCCESS: Checkout should work now!")
            print(f"   User should be able to purchase '{product_name}'")
        elif real_stock == 0:
            print("ℹ️ INFO: Product is actually out of stock")
            print(f"   '{product_name}' has no available stock")
        else:
            print("❌ ISSUE: Checkout logic may still have problems")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    test_video_nuoi_kenh_checkout()
